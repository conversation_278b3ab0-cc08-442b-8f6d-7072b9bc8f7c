# AiMusic 音乐生成项目 - 完整设计文档

## 项目概述

AiMusic是一个基于AI的音乐生成平台，提供文本到音乐的生成服务，包含提示词优化功能。

### 技术栈
- **前端**: Vue 3 + Vite + Element Plus
- **后端**: Node.js + Express + TypeScript
- **数据库**: MySQL 8.0
- **AI服务**: Suno API (主要) + OpenAI GPT (提示词优化)
- **文件存储**: 本地存储 + 云存储(可选)

## 需求分析

### 核心功能
1. **用户输入界面** - 多行文本输入，支持详细音乐描述
2. **提示词优化** - 使用GPT优化用户输入，生成专业音乐描述
3. **音乐生成** - 集成Suno API，支持多种风格和时长
4. **数据管理** - 完整的用户生成记录和历史管理
5. **用户体验** - 响应式设计，音乐播放器，文件下载

### 技术调研结果

#### Suno API 分析
- **优势**: 
  - 支持V3.5/V4/V4.5多个模型版本
  - 20秒流式输出，响应快速
  - 无水印商业使用
  - 支持最长8分钟音乐生成
  - 完整的回调机制
- **定价**: 按使用量计费，透明定价
- **集成**: RESTful API，支持多种编程语言

#### 替代方案
- **MusicGen**: Meta开源模型，需要本地部署
- **其他API**: UdioAPI等第三方服务

## 项目架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue 3 前端    │    │  Node.js 后端   │    │   MySQL 数据库   │
│                 │    │                 │    │                 │
│ - 用户界面      │◄──►│ - RESTful API   │◄──►│ - 用户数据      │
│ - 音乐播放器    │    │ - AI集成        │    │ - 生成记录      │
│ - 文件管理      │    │ - 文件处理      │    │ - 提示词记录    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   外部AI服务     │
                    │                 │
                    │ - Suno API      │
                    │ - OpenAI GPT    │
                    └─────────────────┘
```

### 目录结构
```
AiMusic/
├── frontend/                 # Vue 3前端项目
│   ├── src/
│   │   ├── components/       # 组件
│   │   ├── views/           # 页面
│   │   ├── api/             # API调用
│   │   ├── utils/           # 工具函数
│   │   └── assets/          # 静态资源
│   ├── public/
│   └── package.json
├── backend/                  # Node.js后端项目
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── services/        # 业务逻辑
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   ├── uploads/             # 文件上传目录
│   └── package.json
├── database/                 # 数据库脚本
│   ├── migrations/          # 数据库迁移
│   └── seeds/               # 初始数据
└── docs/                    # 项目文档
```

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 音乐生成记录表 (music_generations)
```sql
CREATE TABLE music_generations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_prompt TEXT NOT NULL,
    optimized_prompt TEXT,
    music_style VARCHAR(100),
    duration INT DEFAULT 30,
    model_version VARCHAR(20) DEFAULT 'V4',
    suno_task_id VARCHAR(100),
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    audio_file_path VARCHAR(500),
    audio_url VARCHAR(500),
    file_size INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

### 提示词优化记录表 (prompt_optimizations)
```sql
CREATE TABLE prompt_optimizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_prompt TEXT NOT NULL,
    optimized_prompt TEXT NOT NULL,
    optimization_type VARCHAR(50) DEFAULT 'gpt-enhancement',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
);
```

## API接口设计

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

### 提示词优化
- `POST /api/prompts/optimize` - 优化提示词
- `GET /api/prompts/history` - 获取优化历史

### 音乐生成
- `POST /api/music/generate` - 生成音乐
- `GET /api/music/status/:taskId` - 查询生成状态
- `GET /api/music/history` - 获取生成历史
- `GET /api/music/download/:id` - 下载音乐文件
- `DELETE /api/music/:id` - 删除音乐记录

### 文件管理
- `POST /api/files/upload` - 文件上传
- `GET /api/files/:filename` - 文件访问

## 前端组件设计

### 主要组件
1. **MusicGenerator.vue** - 主要音乐生成界面
2. **PromptOptimizer.vue** - 提示词优化组件
3. **MusicPlayer.vue** - 音乐播放器组件
4. **GenerationHistory.vue** - 生成历史组件
5. **UserProfile.vue** - 用户资料组件

### 状态管理 (Pinia)
```javascript
// stores/music.js
export const useMusicStore = defineStore('music', {
  state: () => ({
    currentGeneration: null,
    generationHistory: [],
    isGenerating: false,
    optimizedPrompt: ''
  }),
  actions: {
    async generateMusic(prompt, options) { /* ... */ },
    async optimizePrompt(prompt) { /* ... */ },
    async fetchHistory() { /* ... */ }
  }
})
```

## 后端服务设计

### 核心服务类
1. **SunoService** - Suno API集成
2. **OpenAIService** - GPT提示词优化
3. **MusicGenerationService** - 音乐生成业务逻辑
4. **FileService** - 文件处理服务
5. **UserService** - 用户管理服务

### 关键实现点
- 异步任务处理 (使用队列系统)
- 文件上传和存储管理
- API限流和错误处理
- 数据库连接池管理
- 日志记录和监控

## 部署方案

### 开发环境
- 前端: `npm run dev` (Vite开发服务器)
- 后端: `npm run dev` (nodemon热重载)
- 数据库: Docker MySQL容器

### 生产环境
- 前端: Nginx静态文件服务
- 后端: PM2进程管理
- 数据库: MySQL主从复制
- 反向代理: Nginx
- SSL证书: Let's Encrypt

## 安全考虑

1. **API安全**: JWT认证，请求限流
2. **数据安全**: 密码加密，SQL注入防护
3. **文件安全**: 文件类型验证，大小限制
4. **隐私保护**: 用户数据加密存储

## 性能优化

1. **前端优化**: 代码分割，懒加载，CDN
2. **后端优化**: 数据库索引，缓存策略
3. **文件优化**: 音频压缩，分片上传
4. **网络优化**: HTTP/2，Gzip压缩

## 监控和日志

1. **应用监控**: 性能指标，错误追踪
2. **业务监控**: 生成成功率，用户活跃度
3. **日志管理**: 结构化日志，日志轮转
4. **告警机制**: 异常告警，性能告警

## 开发计划

### 第一阶段 (基础功能)
- 用户认证系统
- 基础音乐生成功能
- 简单的前端界面

### 第二阶段 (核心功能)
- 提示词优化功能
- 音乐播放器
- 生成历史管理

### 第三阶段 (完善功能)
- 响应式设计优化
- 性能优化
- 部署和监控

## 风险评估

1. **技术风险**: API稳定性，第三方服务依赖
2. **成本风险**: AI服务费用，存储成本
3. **法律风险**: 版权问题，数据隐私
4. **运营风险**: 用户增长，服务扩展

## 总结

本项目采用现代化的技术栈，设计了完整的前后端分离架构，具备良好的扩展性和维护性。通过合理的数据库设计和API设计，能够支持项目的长期发展需求。
